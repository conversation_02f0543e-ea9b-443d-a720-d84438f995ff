/* ===== CSS VARIABLES ===== */
:root {
  /* Light Theme Colors - More Formal */
  --bg-primary: #ffffff;
  --bg-secondary: #f7f9fc;
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-heading: #2d3748;
  --accent-color: #3182ce;
  --accent-hover: #2c5aa0;
  --border-color: #e2e8f0;
  --shadow-light: rgba(0, 0, 0, 0.08);
  --shadow-medium: rgba(0, 0, 0, 0.12);
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;
  
  /* Spacing */
  --section-padding: 100px 0;
  --container-padding: 0 20px;
  --border-radius: 8px;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Dark Theme Colors */
[data-theme="dark"] {
  --bg-primary: #0a192f;
  --bg-secondary: #112240;
  --text-primary: #ccd6f6;
  --text-secondary: #8892b0;
  --text-heading: #ccd6f6;
  --accent-color: #64ffda;
  --accent-hover: #4fd1c7;
  --border-color: #233554;
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-medium: rgba(0, 0, 0, 0.4);
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: background-color var(--transition-medium), color var(--transition-medium);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--container-padding);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-heading);
  font-weight: 600;
  line-height: 1.2;
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }

p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--accent-hover);
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: rgba(10, 25, 47, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 0;
  transition: all var(--transition-medium);
}

[data-theme="dark"] .navbar {
  background-color: rgba(10, 25, 47, 0.95);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo a {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
  font-family: var(--font-mono);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  color: var(--text-primary);
  font-weight: 400;
  position: relative;
  padding: 0.5rem 0;
  transition: color var(--transition-fast);
}

.nav-link:hover {
  color: var(--accent-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent-color);
  transition: width var(--transition-fast);
}

.nav-link:hover::after {
  width: 100%;
}

.nav-toggle {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-toggle {
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
}

.theme-toggle:hover {
  color: var(--accent-color);
  background-color: var(--bg-secondary);
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  transition: all var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 120px 0 80px;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  position: relative;
  z-index: 1;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--container-padding);
}

.hero-content {
  max-width: 600px;
  animation: fadeInUp 1s ease-out;
  opacity: 1;
  visibility: visible;
}

.hero-greeting {
  color: var(--accent-color);
  font-family: var(--font-mono);
  font-size: 1rem;
  margin-bottom: 1rem;
}

.hero-name {
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-heading);
}

.hero-title {
  font-size: clamp(2rem, 6vw, 4rem);
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.hero-description {
  font-size: 1.1rem;
  max-width: 500px;
  margin-bottom: 3rem;
  line-height: 1.7;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: 1rem;
  transition: all var(--transition-medium);
  text-transform: uppercase;
  letter-spacing: 1px;
  text-decoration: none;
}

.cta-button.primary {
  background-color: var(--accent-color);
  color: white;
  border: 2px solid var(--accent-color);
}

.cta-button.primary:hover {
  background-color: var(--accent-hover);
  border-color: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(49, 130, 206, 0.3);
}

.cta-button.secondary {
  background-color: transparent;
  color: var(--accent-color);
  border: 2px solid var(--accent-color);
}

.cta-button.secondary:hover {
  background-color: var(--accent-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(49, 130, 206, 0.2);
}

/* ===== SECTION STYLES ===== */
section {
  padding: var(--section-padding);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color), var(--accent-hover));
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* ===== ABOUT SECTION ===== */
.about {
  background-color: var(--bg-secondary);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-description p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.7;
}

.about-image {
  display: flex;
  justify-content: center;
}

.image-wrapper {
  position: relative;
  max-width: 300px;
}

.profile-image {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  box-shadow: 0 20px 40px var(--shadow-medium);
  transition: transform var(--transition-medium);
}

.profile-image:hover {
  transform: translateY(-5px);
}

.image-wrapper::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  width: 100%;
  height: 100%;
  border: 2px solid var(--accent-color);
  border-radius: var(--border-radius);
  z-index: -1;
  transition: transform var(--transition-medium);
}

.image-wrapper:hover::before {
  transform: translate(-10px, -10px);
}

/* ===== SKILLS SECTION ===== */
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.skill-category {
  background-color: var(--bg-secondary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px var(--shadow-light);
  transition: transform var(--transition-medium);
}

.skill-category:hover {
  transform: translateY(-5px);
}

.category-title {
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  color: var(--accent-color);
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.skill-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--bg-primary);
  border-radius: 20px;
  font-size: 0.9rem;
  transition: all var(--transition-fast);
}

.skill-item:hover {
  background-color: var(--accent-color);
  color: white;
  transform: scale(1.05);
}

.skill-item i {
  font-size: 1.2rem;
}

/* ===== PROJECTS SECTION ===== */
.projects {
  background-color: var(--bg-secondary);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.project-card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 5px 15px var(--shadow-light);
  transition: all var(--transition-medium);
  position: relative;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px var(--shadow-medium);
}

.project-image {
  position: relative;
  overflow: hidden;
  height: 200px;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-medium);
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(10, 25, 47, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-links {
  display: flex;
  gap: 1rem;
}

.project-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: var(--accent-color);
  color: var(--bg-primary);
  border-radius: 50%;
  font-size: 1.2rem;
  transition: all var(--transition-fast);
}

.project-link:hover {
  background-color: var(--accent-hover);
  transform: scale(1.1);
}

.project-content {
  padding: 1.5rem;
}

.project-title {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: var(--text-heading);
}

.project-description {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  padding: 0.3rem 0.8rem;
  background-color: var(--bg-secondary);
  color: var(--accent-color);
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid var(--accent-color);
}

/* ===== EDUCATION SECTION ===== */
.education {
  background-color: var(--bg-secondary);
}

.education-timeline {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.education-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--accent-color);
  transform: translateX(-50%);
}

.education-item {
  position: relative;
  margin-bottom: 3rem;
  width: 50%;
}

.education-item.left {
  left: 0;
  padding-right: 2rem;
}

.education-item.right {
  left: 50%;
  padding-left: 2rem;
}

.education-item::before {
  content: '';
  position: absolute;
  top: 20px;
  width: 20px;
  height: 20px;
  background-color: var(--accent-color);
  border-radius: 50%;
  border: 4px solid var(--bg-primary);
  z-index: 2;
}

.education-item.left::before {
  right: -11px;
}

.education-item.right::before {
  left: -11px;
}

.education-content {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px var(--shadow-light);
  position: relative;
  transition: all var(--transition-medium);
}

.education-content:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px var(--shadow-medium);
}

.education-content::before {
  content: '';
  position: absolute;
  top: 20px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
}

.education-item.left .education-content::before {
  right: -20px;
  border-left-color: var(--bg-primary);
}

.education-item.right .education-content::before {
  left: -20px;
  border-right-color: var(--bg-primary);
}

.education-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.education-icon {
  font-size: 2rem;
  color: var(--accent-color);
  margin-right: 1rem;
}

.education-info h3 {
  font-size: 1.3rem;
  color: var(--text-heading);
  margin-bottom: 0.3rem;
}

.education-institution {
  color: var(--accent-color);
  font-weight: 500;
  margin-bottom: 0.3rem;
}

.education-duration {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-family: var(--font-mono);
}

.education-details {
  margin-top: 1rem;
}

.education-field {
  font-weight: 500;
  color: var(--text-heading);
  margin-bottom: 0.5rem;
}

.education-location {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.education-grade {
  background-color: var(--bg-secondary);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: inline-block;
  font-weight: 500;
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
}

/* ===== INTERNSHIPS/TIMELINE SECTION ===== */
.internships {
  background-color: var(--bg-secondary);
}

.timeline {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--accent-color);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 3rem;
  width: 50%;
}

.timeline-item.left {
  left: 0;
  padding-right: 2rem;
}

.timeline-item.right {
  left: 50%;
  padding-left: 2rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  top: 20px;
  width: 20px;
  height: 20px;
  background-color: var(--accent-color);
  border-radius: 50%;
  border: 4px solid var(--bg-primary);
  z-index: 2;
}

.timeline-item.left::before {
  right: -11px;
}

.timeline-item.right::before {
  left: -11px;
}

.timeline-content {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px var(--shadow-light);
  position: relative;
  transition: all var(--transition-medium);
}

.timeline-content:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px var(--shadow-medium);
}

.timeline-content::before {
  content: '';
  position: absolute;
  top: 20px;
  width: 0;
  height: 0;
  border: 10px solid transparent;
}

.timeline-item.left .timeline-content::before {
  right: -20px;
  border-left-color: var(--bg-primary);
}

.timeline-item.right .timeline-content::before {
  left: -20px;
  border-right-color: var(--bg-primary);
}

.timeline-header {
  margin-bottom: 1rem;
}

.timeline-title {
  font-size: 1.3rem;
  color: var(--text-heading);
  margin-bottom: 0.5rem;
}

.timeline-company {
  color: var(--accent-color);
  font-weight: 500;
  margin-bottom: 0.3rem;
}

.timeline-duration {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-family: var(--font-mono);
}

.timeline-description {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.timeline-responsibilities {
  margin-bottom: 1rem;
  padding-left: 1.2rem;
}

.timeline-responsibilities li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.timeline-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.timeline-certificate {
  margin-top: 1rem;
}

/* ===== ACHIEVEMENTS SECTION ===== */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.achievement-item {
  background-color: var(--bg-secondary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px var(--shadow-light);
  text-align: center;
  transition: all var(--transition-medium);
}

.achievement-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px var(--shadow-medium);
}

.achievement-icon {
  font-size: 3rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
}

.achievement-icon-img {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 8px;
}

.achievement-title {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: var(--text-heading);
}

.achievement-organization {
  color: var(--accent-color);
  font-weight: 500;
  margin-bottom: 0.3rem;
}

.achievement-date {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-family: var(--font-mono);
  margin-bottom: 1rem;
}

.achievement-description {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.achievement-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: var(--accent-color);
  color: white;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  text-decoration: none;
}

.achievement-link:hover {
  background-color: var(--accent-hover);
  color: white;
  transform: translateY(-2px);
}

.certificate-link {
  background-color: #28a745;
}

.certificate-link:hover {
  background-color: #218838;
}



/* ===== CONTACT SECTION ===== */
.contact-content {
  max-width: 1200px;
  margin: 0 auto;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-info {
  text-align: left;
}

.contact-info-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--text-heading);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.contact-item i {
  font-size: 1.5rem;
  color: var(--accent-color);
  width: 24px;
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: var(--bg-secondary);
  border-radius: 50%;
  font-size: 1.5rem;
  transition: all var(--transition-medium);
}

.social-link:hover {
  background-color: var(--accent-color);
  color: white;
  transform: translateY(-3px);
}

/* ===== CONTACT FORM ===== */
.contact-form-container {
  background-color: var(--bg-secondary);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px var(--shadow-light);
}

.contact-form-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--text-heading);
  text-align: center;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-heading);
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-family: var(--font-primary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--accent-color);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.form-submit-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-medium);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.form-submit-btn:hover {
  background-color: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(49, 130, 206, 0.3);
}

.form-submit-btn:active {
  transform: translateY(0);
}

.form-alternative {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.form-alternative p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.email-direct-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  color: var(--accent-color);
  border: 2px solid var(--accent-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all var(--transition-medium);
}

.email-direct-btn:hover {
  background-color: var(--accent-color);
  color: white;
  transform: translateY(-2px);
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--bg-secondary);
  padding: 2rem 0;
  border-top: 1px solid var(--border-color);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social .social-link {
  width: 40px;
  height: 40px;
  font-size: 1.2rem;
}

/* ===== UTILITY CLASSES ===== */
.fade-in {
  animation: fadeInUp 1s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.text-center { text-align: center; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-hover);
}

/* ===== SELECTION STYLING ===== */
::selection {
  background-color: var(--accent-color);
  color: var(--bg-primary);
}

::-moz-selection {
  background-color: var(--accent-color);
  color: var(--bg-primary);
}

/* ===== FOCUS STYLES ===== */
.keyboard-navigation *:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* ===== SKIP LINK ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--accent-color);
  color: var(--bg-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: 0 0 4px 4px;
  z-index: 10001;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 0;
}

/* ===== MESSAGE STYLES ===== */
.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  color: white;
  font-weight: 500;
  z-index: 10000;
  transform: translateX(100%);
  transition: transform var(--transition-medium);
  max-width: 300px;
}

.message.show {
  transform: translateX(0);
}

.message-success {
  background-color: #10b981;
}

.message-error {
  background-color: #ef4444;
}

.message-info {
  background-color: var(--accent-color);
  color: var(--bg-primary);
}

/* ===== LOADING STATES ===== */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--accent-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ===== NAVBAR SCROLLED STATE ===== */
.navbar.scrolled {
  background-color: rgba(10, 25, 47, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* ===== SMOOTH TRANSITIONS ===== */
* {
  transition: background-color var(--transition-medium),
              color var(--transition-medium),
              border-color var(--transition-medium);
}
