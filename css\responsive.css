/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
  
  .hero-content {
    max-width: 700px;
  }
  
  .section-padding {
    padding: 120px 0;
  }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
  .container {
    max-width: 960px;
  }
  
  .nav-container {
    padding: 0 30px;
  }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
  .container {
    max-width: 720px;
    padding: 0 30px;
  }
  
  /* Navigation */
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: var(--bg-primary);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
    gap: 1rem;
  }
  
  .nav-menu.active {
    left: 0;
  }
  
  .hamburger {
    display: flex;
  }
  
  .hamburger.active .bar:nth-child(2) {
    opacity: 0;
  }
  
  .hamburger.active .bar:nth-child(1) {
    transform: translateY(7px) rotate(45deg);
  }
  
  .hamburger.active .bar:nth-child(3) {
    transform: translateY(-7px) rotate(-45deg);
  }
  
  /* Hero Section */
  .hero {
    padding: 120px 0 80px;
    text-align: center;
  }
  
  .hero-name {
    font-size: clamp(2.5rem, 6vw, 4rem);
  }
  
  .hero-title {
    font-size: clamp(1.8rem, 5vw, 3rem);
  }
  
  /* About Section */
  .about-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
  
  .about-image {
    order: -1;
  }
  
  .image-wrapper {
    max-width: 250px;
  }
  
  /* Skills Section */
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .skill-category {
    padding: 1.5rem;
  }
  
  /* Projects Section */
  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  /* Section Spacing */
  section {
    padding: 80px 0;
  }
  
  .section-header {
    margin-bottom: 3rem;
  }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
  .container {
    max-width: 540px;
    padding: 0 20px;
  }
  
  /* Typography */
  h1 { font-size: clamp(2rem, 6vw, 3rem); }
  h2 { font-size: clamp(1.5rem, 5vw, 2.5rem); }
  h3 { font-size: clamp(1.2rem, 4vw, 1.8rem); }
  
  /* Navigation */
  .nav-container {
    padding: 0 20px;
    height: 60px;
  }
  
  .nav-logo a {
    font-size: 1.3rem;
  }
  
  .nav-menu {
    top: 60px;
    padding: 1.5rem 0;
  }
  
  .theme-toggle {
    font-size: 1rem;
    padding: 0.4rem;
  }
  
  /* Hero Section */
  .hero {
    padding: 100px 0 60px;
    min-height: 90vh;
  }
  
  .hero-name {
    font-size: clamp(2rem, 8vw, 3.5rem);
    margin-bottom: 1rem;
  }
  
  .hero-title {
    font-size: clamp(1.5rem, 6vw, 2.5rem);
    margin-bottom: 1.5rem;
  }
  
  .hero-description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }
  
  .cta-button {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
  }
  
  /* About Section */
  .about-content {
    gap: 2rem;
  }
  
  .about-description p {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
  
  .image-wrapper {
    max-width: 200px;
  }
  
  /* Skills Section */
  .skills-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .skill-category {
    padding: 1.2rem;
  }
  
  .category-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }
  
  .skill-item {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  /* Projects Section */
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  /* Contact Section */
  .contact-item {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .social-links {
    gap: 1rem;
  }
  
  .social-link {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }
  
  /* Footer */
  .footer-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .footer-social .social-link {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  /* Section Spacing */
  section {
    padding: 60px 0;
  }
  
  .section-header {
    margin-bottom: 2rem;
  }
  
  .section-title::after {
    width: 40px;
    height: 2px;
  }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
  .container {
    padding: 0 15px;
  }
  
  /* Navigation */
  .nav-container {
    padding: 0 15px;
  }
  
  /* Hero Section */
  .hero {
    padding: 80px 0 40px;
  }
  
  .hero-content {
    max-width: 100%;
  }
  
  .hero-name {
    font-size: clamp(1.8rem, 10vw, 3rem);
  }
  
  .hero-title {
    font-size: clamp(1.3rem, 8vw, 2rem);
  }
  
  .hero-description {
    font-size: 0.95rem;
  }
  
  .cta-button {
    padding: 0.7rem 1.2rem;
    font-size: 0.85rem;
  }
  
  /* About Section */
  .image-wrapper {
    max-width: 180px;
  }
  
  /* Skills Section */
  .skill-category {
    padding: 1rem;
  }
  
  .skills-list {
    gap: 0.5rem;
  }
  
  .skill-item {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
  }
  
  .skill-item i {
    font-size: 1rem;
  }
  
  /* Contact Section */
  .contact-item {
    flex-direction: column;
    gap: 0.5rem;
    font-size: 0.95rem;
  }
  
  .social-links {
    gap: 0.8rem;
  }
  
  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  /* Section Spacing */
  section {
    padding: 50px 0;
  }
  
  .section-header {
    margin-bottom: 1.5rem;
  }
}

/* Extra Small Mobile (up to 375px) */
@media (max-width: 375px) {
  .container {
    padding: 0 10px;
  }
  
  .nav-container {
    padding: 0 10px;
  }
  
  .hero-name {
    font-size: clamp(1.5rem, 12vw, 2.5rem);
  }
  
  .hero-title {
    font-size: clamp(1.1rem, 10vw, 1.8rem);
  }
  
  .cta-button {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }
  
  .skill-item {
    font-size: 0.7rem;
  }
  
  .social-link {
    width: 35px;
    height: 35px;
    font-size: 1.1rem;
  }
}

/* Landscape Mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .hero {
    min-height: 100vh;
    padding: 60px 0 40px;
  }
  
  .nav-container {
    height: 50px;
  }
  
  .nav-menu {
    top: 50px;
  }
  
  section {
    padding: 40px 0;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .profile-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  .navbar,
  .theme-toggle,
  .hamburger,
  .cta-button,
  .social-links {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  h1, h2, h3 {
    page-break-after: avoid;
  }
  
  section {
    page-break-inside: avoid;
    padding: 20px 0;
  }
}
