// ===== MAIN JAVASCRIPT FILE =====

// DOM Elements
const navbar = document.getElementById('navbar');
const hamburger = document.getElementById('hamburger');
const navMenu = document.querySelector('.nav-menu');
const themeToggle = document.getElementById('theme-toggle');
const navLinks = document.querySelectorAll('.nav-link');

// ===== THEME MANAGEMENT =====
class ThemeManager {
  constructor() {
    this.currentTheme = localStorage.getItem('theme') || 'light';
    this.init();
  }

  init() {
    this.setTheme(this.currentTheme);
    this.bindEvents();
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    this.currentTheme = theme;
    localStorage.setItem('theme', theme);
    this.updateThemeIcon();
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  updateThemeIcon() {
    const icon = themeToggle.querySelector('i');
    if (this.currentTheme === 'dark') {
      icon.className = 'fas fa-sun';
    } else {
      icon.className = 'fas fa-moon';
    }
  }

  bindEvents() {
    themeToggle.addEventListener('click', () => this.toggleTheme());
  }
}

// ===== NAVIGATION MANAGEMENT =====
class NavigationManager {
  constructor() {
    this.isMenuOpen = false;
    this.init();
  }

  init() {
    this.bindEvents();
    this.handleScroll();
  }

  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
    
    // Prevent body scroll when menu is open
    document.body.style.overflow = this.isMenuOpen ? 'hidden' : '';
  }

  closeMenu() {
    this.isMenuOpen = false;
    hamburger.classList.remove('active');
    navMenu.classList.remove('active');
    document.body.style.overflow = '';
  }

  handleScroll() {
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      // Add/remove scrolled class for navbar styling
      if (scrollTop > 50) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }
      
      // Hide/show navbar on scroll
      if (scrollTop > lastScrollTop && scrollTop > 100) {
        navbar.style.transform = 'translateY(-100%)';
      } else {
        navbar.style.transform = 'translateY(0)';
      }
      
      lastScrollTop = scrollTop;
    });
  }

  bindEvents() {
    // Hamburger menu toggle
    hamburger.addEventListener('click', () => this.toggleMenu());
    
    // Close menu when clicking nav links
    navLinks.forEach(link => {
      link.addEventListener('click', () => this.closeMenu());
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
      if (this.isMenuOpen && !navbar.contains(e.target)) {
        this.closeMenu();
      }
    });
    
    // Handle escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isMenuOpen) {
        this.closeMenu();
      }
    });
  }
}

// ===== SMOOTH SCROLLING =====
class SmoothScroll {
  constructor() {
    this.init();
  }

  init() {
    this.bindEvents();
  }

  scrollToElement(targetId) {
    const targetElement = document.querySelector(targetId);
    if (targetElement) {
      const offsetTop = targetElement.offsetTop - 70; // Account for fixed navbar
      
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
  }

  bindEvents() {
    // Handle navigation links
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href');
        this.scrollToElement(targetId);
      });
    });
    
    // Handle CTA button
    const ctaButton = document.querySelector('.cta-button');
    if (ctaButton) {
      ctaButton.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = ctaButton.getAttribute('href');
        this.scrollToElement(targetId);
      });
    }
  }
}

// ===== ACTIVE SECTION HIGHLIGHTING =====
class ActiveSectionManager {
  constructor() {
    this.sections = document.querySelectorAll('section[id]');
    this.init();
  }

  init() {
    this.bindEvents();
  }

  updateActiveSection() {
    const scrollPosition = window.scrollY + 100;
    
    this.sections.forEach(section => {
      const sectionTop = section.offsetTop;
      const sectionHeight = section.offsetHeight;
      const sectionId = section.getAttribute('id');
      const correspondingLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
      
      if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
        // Remove active class from all links
        navLinks.forEach(link => link.classList.remove('active'));
        
        // Add active class to current section link
        if (correspondingLink) {
          correspondingLink.classList.add('active');
        }
      }
    });
  }

  bindEvents() {
    window.addEventListener('scroll', () => this.updateActiveSection());
  }
}

// ===== FORM HANDLING =====
class FormHandler {
  constructor() {
    this.init();
  }

  init() {
    this.bindEvents();
  }

  handleContactForm(formData) {
    // Simulate form submission
    console.log('Form submitted:', formData);
    
    // Show success message
    this.showMessage('Thank you for your message! I\'ll get back to you soon.', 'success');
  }

  showMessage(message, type = 'info') {
    // Create message element
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;
    
    // Add to page
    document.body.appendChild(messageEl);
    
    // Animate in
    setTimeout(() => messageEl.classList.add('show'), 100);
    
    // Remove after delay
    setTimeout(() => {
      messageEl.classList.remove('show');
      setTimeout(() => messageEl.remove(), 300);
    }, 3000);
  }

  bindEvents() {
    // Handle contact form if it exists
    const contactForm = document.querySelector('#contact-form');
    if (contactForm) {
      contactForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const submitBtn = contactForm.querySelector('.form-submit-btn');
        const originalText = submitBtn.innerHTML;

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        submitBtn.disabled = true;

        try {
          let success = false;

          // Try local Node.js server first
          try {
            const formData = {
              name: contactForm.querySelector('#name').value,
              email: contactForm.querySelector('#email').value,
              subject: contactForm.querySelector('#subject').value,
              message: contactForm.querySelector('#message').value
            };

            const response = await fetch('/send-email', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(formData)
            });

            const result = await response.json();

            if (result.success) {
              success = true;
              this.showMessage('Thank you! Your message has been sent successfully. I\'ll get back to you soon.', 'success');
              contactForm.reset();
            }
          } catch (serverError) {
            console.log('Local server not available, trying Web3Forms...');
          }

          // If local server fails, try Web3Forms
          if (!success) {
            try {
              const formData = new FormData(contactForm);

              const response = await fetch('https://api.web3forms.com/submit', {
                method: 'POST',
                body: formData
              });

              const result = await response.json();

              if (result.success) {
                success = true;
                this.showMessage('Thank you! Your message has been sent successfully. I\'ll get back to you soon.', 'success');
                contactForm.reset();
              }
            } catch (web3Error) {
              console.log('Web3Forms failed, using mailto...');
            }
          }

          // If all services fail, create mailto link
          if (!success) {
            this.createMailtoLink(contactForm);
          }

        } catch (error) {
          console.error('Form submission error:', error);
          // If all fails, create mailto link
          this.createMailtoLink(contactForm);
        } finally {
          // Reset button
          submitBtn.innerHTML = originalText;
          submitBtn.disabled = false;
        }
      });
    }
  }

  createMailtoLink(form) {
    const name = form.querySelector('#name').value;
    const email = form.querySelector('#email').value;
    const subject = form.querySelector('#subject').value;
    const message = form.querySelector('#message').value;

    const mailtoSubject = encodeURIComponent(`Portfolio Contact: ${subject}`);
    const mailtoBody = encodeURIComponent(
      `Hi Disha,\n\n` +
      `Name: ${name}\n` +
      `Email: ${email}\n` +
      `Subject: ${subject}\n\n` +
      `Message:\n${message}\n\n` +
      `Best regards,\n${name}`
    );

    const mailtoLink = `mailto:<EMAIL>?subject=${mailtoSubject}&body=${mailtoBody}`;

    // Open mailto link
    window.location.href = mailtoLink;

    this.showMessage('Opening your email client to send the message. If it doesn\'t open automatically, please use the "Email Directly" button below.', 'info');
    form.reset();
  }
}

// ===== PERFORMANCE UTILITIES =====
class PerformanceUtils {
  static throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  static debounce(func, delay) {
    let timeoutId;
    return function() {
      const args = arguments;
      const context = this;
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(context, args), delay);
    };
  }

  static preloadImages(imageUrls) {
    imageUrls.forEach(url => {
      const img = new Image();
      img.src = url;
    });
  }
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
class AccessibilityManager {
  constructor() {
    this.init();
  }

  init() {
    this.handleKeyboardNavigation();
    this.handleFocusManagement();
    this.addSkipLinks();
  }

  handleKeyboardNavigation() {
    // Tab navigation for mobile menu
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab' && navMenu.classList.contains('active')) {
        const focusableElements = navMenu.querySelectorAll('a, button');
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    });
  }

  handleFocusManagement() {
    // Ensure focus is visible
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });
    
    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }

  addSkipLinks() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.className = 'skip-link';
    skipLink.textContent = 'Skip to main content';
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  }
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
  // Initialize all managers
  const themeManager = new ThemeManager();
  const navigationManager = new NavigationManager();
  const smoothScroll = new SmoothScroll();
  const activeSectionManager = new ActiveSectionManager();
  const formHandler = new FormHandler();
  const accessibilityManager = new AccessibilityManager();

  // Initialize AOS animations
  if (typeof AOS !== 'undefined') {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100
    });
  }

  // Add loading complete class
  setTimeout(() => {
    document.body.classList.add('loaded');
  }, 100);

  console.log('Portfolio website initialized successfully!');
});

// ===== GLOBAL ERROR HANDLING =====
window.addEventListener('error', (e) => {
  console.error('Global error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
  console.error('Unhandled promise rejection:', e.reason);
});

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    ThemeManager,
    NavigationManager,
    SmoothScroll,
    ActiveSectionManager,
    FormHandler,
    PerformanceUtils,
    AccessibilityManager
  };
}
