# Disha T V - Portfolio Website

A stunning, professional, and interactive portfolio website for Disha T V, featuring modern design, dark mode, and full responsiveness across all devices.

## 🌟 Features

### Design & User Experience
- **Modern & Professional Design**: Clean, elegant interface with a sophisticated color palette
- **Dark Mode Toggle**: Seamless switching between light and dark themes with smooth transitions
- **Fully Responsive**: Optimized for all screen sizes from mobile phones to widescreen desktops
- **Smooth Animations**: On-scroll animations, hover effects, and interactive elements
- **Fast Loading**: Optimized performance with efficient code and asset loading

### Color Palette
- **Primary Colors**: Deep Navy Blue (#0A192F), Clean White (#FFFFFF), Vibrant Teal (#64FFDA)
- **Secondary Colors**: Light Grey (#8892B0) for body text, Bright Grey (#CCD6F6) for headings
- **Dark Mode**: Automatic color scheme adaptation with proper contrast ratios

### Typography
- **Primary Font**: Inter - Clean, modern sans-serif for headings and body text
- **Monospace Font**: JetBrains Mono - For code snippets and technical elements

## 📱 Sections

1. **Navigation Bar**
   - Sticky/Fixed positioning
   - Smooth scroll navigation
   - Dark mode toggle
   - Mobile hamburger menu

2. **Hero Section**
   - Engaging introduction
   - Call-to-action button
   - Animated text effects

3. **About Me**
   - Two-column layout
   - Professional headshot
   - Engaging biography

4. **Skills**
   - Grid layout with categories:
     - Languages (Python, JavaScript, Java, HTML5, CSS3)
     - Frameworks (React, Node.js, Django, Bootstrap)
     - Tools (Git, Docker, Figma, VS Code)
     - Databases (MySQL, MongoDB, PostgreSQL)

5. **Projects**
   - Responsive grid of project cards
   - Live demo and source code links
   - Technology tags
   - Hover effects and overlays

6. **Internships/Experience**
   - Vertical timeline layout
   - Company details and accomplishments
   - Technology stacks used

7. **Achievements & Certifications**
   - Clean card-based layout
   - Awards, hackathon wins, and certifications
   - Verification links where applicable

8. **Contact**
   - Social media links
   - Email contact
   - Professional networking links

## 🚀 Getting Started

### Prerequisites
- A modern web browser (Chrome, Firefox, Safari, Edge)
- No server setup required - runs directly in the browser

### Installation
1. Clone or download the repository
2. Open `index.html` in your web browser
3. That's it! The website is ready to use

### File Structure
```
portfolio/
├── index.html              # Main HTML file
├── css/
│   ├── styles.css          # Main stylesheet
│   ├── animations.css      # Animation definitions
│   └── responsive.css      # Responsive design rules
├── js/
│   ├── main.js            # Core functionality
│   ├── animations.js      # Animation controllers
│   └── data.js           # Portfolio data and content
├── Disha_Image.jpeg       # Profile image
├── Disha_Resume.pdf       # Resume file
└── README.md             # This file
```

## 🎨 Customization

### Updating Personal Information
Edit the `portfolioData` object in `js/data.js` to update:
- Personal details
- Skills and technologies
- Project information
- Experience and achievements
- Social media links

### Changing Colors
Modify the CSS variables in `css/styles.css`:
```css
:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #0a192f;
  --text-secondary: #8892b0;
  --accent-color: #64ffda;
  /* ... other variables */
}
```

### Adding New Sections
1. Add HTML structure in `index.html`
2. Add corresponding styles in `css/styles.css`
3. Update navigation links
4. Add data to `js/data.js` if needed

## 🔧 Technical Features

### Performance Optimizations
- Efficient CSS with minimal redundancy
- Optimized JavaScript with throttling and debouncing
- Lazy loading for images
- Minimal external dependencies

### Accessibility Features
- Semantic HTML structure
- Proper ARIA labels
- Keyboard navigation support
- High contrast ratios
- Screen reader compatibility
- Skip links for navigation

### Browser Compatibility
- Modern browsers (Chrome 60+, Firefox 60+, Safari 12+, Edge 79+)
- Progressive enhancement for older browsers
- Graceful degradation of advanced features

## 📱 Responsive Breakpoints

- **Extra Large Desktop**: 1200px and up
- **Desktop**: 992px to 1199px
- **Tablet**: 768px to 991px
- **Mobile Large**: 576px to 767px
- **Mobile Small**: up to 575px
- **Extra Small Mobile**: up to 375px

## 🌙 Dark Mode

The website includes a fully functional dark mode with:
- Automatic theme detection based on system preferences
- Manual toggle switch in the navigation
- Smooth transitions between themes
- Persistent theme selection (saved in localStorage)
- Proper color contrast in both modes

## 🎯 SEO & Meta Tags

The website includes proper meta tags for:
- Search engine optimization
- Social media sharing (Open Graph)
- Mobile viewport configuration
- Performance optimization hints

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio. If you find any bugs or have suggestions for improvements, please open an issue or submit a pull request.

## 📞 Contact

- **Email**: <EMAIL>
- **LinkedIn**: [linkedin.com/in/dishatv](https://linkedin.com/in/dishatv)
- **GitHub**: [github.com/dishatv](https://github.com/dishatv)

---

**Built with ❤️ by Disha T V**

*A Creative Technologist & Aspiring Developer*
