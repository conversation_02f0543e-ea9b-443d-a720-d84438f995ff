// ===== DATA JAVASCRIPT FILE =====

// ===== PORTFOLIO DATA =====
const portfolioData = {
  // Personal Information
  personal: {
    name: "<PERSON>sha T V",
    title: "Creative Technologist & Aspiring Developer",
    email: "<EMAIL>",
    location: "India",
    bio: [
      "Hello! I'm <PERSON><PERSON>, a passionate technologist with a love for creating digital experiences that make a difference. My journey in technology began with curiosity and has evolved into a deep commitment to continuous learning and innovation.",
      "I enjoy working on projects that challenge me to think creatively and solve complex problems. Whether it's developing web applications, exploring new frameworks, or collaborating on team projects, I'm always eager to expand my skill set and contribute meaningfully.",
      "When I'm not coding, you can find me exploring the latest tech trends, participating in hackathons, or working on personal projects that push the boundaries of what I know."
    ]
  },

  // Social Links
  social: {
    linkedin: "https://www.linkedin.com/in/disha-t-v-033ab8264",
    github: "https://github.com/disha9353",
    instagram: "https://instagram.com/disha.gowda_",
    salesforce: "https://www.salesforce.com/trailblazer/itbg4hujrhthu29hdf",
    email: "mailto:<EMAIL>"
  },

  // Education Data
  education: [
    {
      id: 1,
      institution: "Adichunchanagiri Institute of Technology",
      degree: "Bachelor of Engineering",
      field: "Computer Science and Engineering",
      location: "Chikkamagaluru, India",
      duration: "Dec 2022 - May 2026",
      grade: "CGPA: 9.41",
      status: "Pursuing",
      icon: "fas fa-graduation-cap"
    },
    {
      id: 2,
      institution: "Shri Bhuvanendra PU College",
      degree: "Pre University",
      field: "Science",
      location: "Karkala, India",
      duration: "Sep 2020 - Mar 2022",
      grade: "Percentage: 97.8%",
      status: "Completed",
      icon: "fas fa-school"
    },
    {
      id: 3,
      institution: "Nalanda High School",
      degree: "SSLC",
      field: "Secondary Education",
      location: "Mudigere, India",
      duration: "June 2017 - July 2020",
      grade: "Percentage: 93.6%",
      status: "Completed",
      icon: "fas fa-book"
    }
  ],

  // Skills Data
  skills: {
    languages: [
      { name: "Python", icon: "fab fa-python", level: 90 },
      { name: "JavaScript", icon: "fab fa-js-square", level: 85 },
      { name: "Java", icon: "fab fa-java", level: 80 },
      { name: "HTML5", icon: "fab fa-html5", level: 95 },
      { name: "CSS3", icon: "fab fa-css3-alt", level: 90 },
      { name: "TypeScript", icon: "fas fa-code", level: 75 }
    ],
    frameworks: [
      { name: "React", icon: "fab fa-react", level: 85 },
      { name: "Node.js", icon: "fab fa-node-js", level: 80 },
      { name: "Django", icon: "fas fa-code", level: 75 },
      { name: "Bootstrap", icon: "fab fa-bootstrap", level: 90 },
      { name: "Express.js", icon: "fas fa-server", level: 70 },
      { name: "Vue.js", icon: "fab fa-vuejs", level: 65 }
    ],
    tools: [
      { name: "Git", icon: "fab fa-git-alt", level: 90 },
      { name: "Docker", icon: "fab fa-docker", level: 70 },
      { name: "Figma", icon: "fab fa-figma", level: 85 },
      { name: "VS Code", icon: "fas fa-code", level: 95 },
      { name: "Terminal", icon: "fas fa-terminal", level: 85 },
      { name: "Postman", icon: "fas fa-paper-plane", level: 80 }
    ],
    databases: [
      { name: "MySQL", icon: "fas fa-database", level: 85 },
      { name: "MongoDB", icon: "fas fa-leaf", level: 80 },
      { name: "PostgreSQL", icon: "fas fa-server", level: 75 },
      { name: "Redis", icon: "fas fa-memory", level: 65 }
    ]
  },

  // Projects Data
  projects: [
    {
      id: 1,
      title: "Namma Naadu",
      description: "An online platform designed to help travelers explore new destinations, plan their trips, and access essential travel-related information.",
      image: "Namma_Naadu.jpg",
      technologies: ["HTML", "CSS", "JavaScript", "PHP"],
      liveDemo: null,
      sourceCode: "https://github.com/disha9353/Namma_Naadu_Travel_Guide",
      featured: true,
      category: "Full Stack"
    },
    {
      id: 2,
      title: "DSA Tracker",
      description: "A tool or system designed to help programmers, students, and competitive coders systematically track their progress in learning and practicing DSA concepts.",
      image: "DSA_Tracker.jpg",
      technologies: ["ReactJS", "Bootstrap", "LocalBase"],
      liveDemo: null,
      sourceCode: "https://github.com/disha9353",
      featured: true,
      category: "Frontend"
    },
    {
      id: 3,
      title: "Blood Donation Management System",
      description: "A Blood Donation Management System is a digital platform designed to streamline the process of blood donation by managing donor registration, blood inventory, and request tracking efficiently.",
      image: "Blood_Donation.jpg",
      technologies: ["Nodemailer", "React", "Supabase"],
      liveDemo: null,
      sourceCode: "https://github.com/disha9353/Blood_Donation_Management_System",
      featured: true,
      category: "Full Stack"
    },
    {
      id: 4,
      title: "Eco Friendly Smart Urban Living",
      description: "An online platform designed for the responsible citizens to report the issues in their surrounding, volunteering to solve the issue, resource sharing, maintaining healthy life through taking challenges etc.",
      image: "Eco_Friendly_Smart_Living.jpg",
      technologies: ["React", "Tailwind CSS", "MongoDB"],
      liveDemo: null,
      sourceCode: "https://github.com/disha9353/Eco_Friendly_Urban_City_Management",
      featured: true,
      category: "Full Stack"
    }
  ],

  // Experience/Internships Data
  experience: [
    {
      id: 1,
      company: "Codsoft",
      position: "Web Development Intern",
      location: "Online",
      duration: "Jun-Jul 2024",
      type: "Online Internship",
      description: "Completed web development internship focusing on modern web technologies and responsive design principles.",
      responsibilities: [
        "Developed responsive web applications using HTML, CSS, and JavaScript",
        "Created interactive user interfaces with modern design principles",
        "Implemented responsive layouts for various screen sizes",
        "Worked on real-world projects to enhance practical skills"
      ],
      technologies: ["HTML5", "CSS3", "JavaScript", "Responsive Design"],
      achievements: [
        "Successfully completed all assigned projects within deadlines",
        "Gained hands-on experience in modern web development practices"
      ],
      certificateLink: "Codsoft.jpg"
    },
    {
      id: 2,
      company: "InternForte",
      position: "DevOps Intern",
      location: "Online",
      duration: "Oct 2024",
      type: "Online Internship",
      description: "Gained experience in DevOps practices, automation, and deployment strategies for modern applications.",
      responsibilities: [
        "Learned containerization technologies and deployment strategies",
        "Worked with CI/CD pipelines and automation tools",
        "Gained experience in cloud platforms and infrastructure management",
        "Participated in DevOps best practices and methodologies"
      ],
      technologies: ["Docker", "CI/CD", "Cloud Platforms", "Automation Tools"],
      achievements: [
        "Successfully completed DevOps training modules",
        "Gained practical knowledge in modern deployment practices"
      ],
      certificateLink: "Internforte.jpg"
    }
  ],

  // Achievements Data
  achievements: [
    {
      id: 1,
      title: "4th Place in National Hackathon",
      organization: "Ignitex - BGSIT, B G Nagar, Mandya",
      date: "2024",
      description: "Secured 4th place in a 24-hour national-level hackathon, Ignitex, organized by BGSIT, B G Nagar, Mandya.",
      icon: "fas fa-trophy",
      category: "Hackathon"
    },
    {
      id: 2,
      title: "Best of Sustainability Track",
      organization: "GEC Hassan",
      date: "2024",
      description: "Awarded Best of Sustainability Track in the hackathon organized by GEC Hassan.",
      icon: "fas fa-medal",
      category: "Hackathon",
      certificateLink: "Best_of_Sustainability.jpg"
    },
    {
      id: 3,
      title: "Advaya Hackathon Participant",
      organization: "BGSCET, Bangalore",
      date: "2024",
      description: "Participated in the 24-hour national-level hackathon Advaya organized by BGSCET, Bangalore.",
      icon: "fas fa-code",
      category: "Hackathon",
      certificateLink: "Advaya_BGSCET.jpg"
    },
    {
      id: 4,
      title: "The Big Biz Theory Participant",
      organization: "Case Club IIT Madras",
      date: "2024",
      description: "Participated in The Big Biz Theory organized by Case Club Indian Institute of Technology(IIT), Madras.",
      icon: "fas fa-briefcase",
      category: "Competition",
      certificateLink: "BIG_BIZ.jpg"
    },
    {
      id: 5,
      title: "Namma Idea Pitching Event",
      organization: "ME-RIISE FOUNDATION, MCE Hassan",
      date: "2024",
      description: "Participated in Namma Idea Pitching Event organized by ME-RIISE FOUNDATION, Malnad College of Engineering, Hassan.",
      icon: "fas fa-lightbulb",
      category: "Competition",
      certificateLink: "ME-RIISE.jpg"
    },
    {
      id: 6,
      title: "hackCSElerate Participant",
      organization: "Adichunchanagiri Institute of Technology",
      date: "2024",
      description: "Certificate of Participation in hackCSElerate, Technical Event such as Debugging and CSS-BATTLE organized by Adichunchanagiri Institute of Technology, Chikkamagaluru.",
      icon: "fas fa-laptop-code",
      category: "Technical Event",
      certificateLink: "hackCSElerate.jpg"
    },
    {
      id: 7,
      title: "Trailblazer Champion Level",
      organization: "Salesforce Trailhead",
      date: "2024",
      description: "Completed the Trailblazer Champion level provided by Trailhead by Salesforce, demonstrating advanced skills in Salesforce ecosystem.",
      iconImage: "Champion.jpg",
      category: "Certification"
    },
    {
      id: 8,
      title: "Trailblazer Innovator Level",
      organization: "Salesforce Trailhead",
      date: "2024",
      description: "Completed the Trailblazer Innovator level provided by Trailhead by Salesforce, showcasing innovation and expertise in Salesforce technologies.",
      iconImage: "Innovator.jpg",
      category: "Certification"
    }
  ],

  // Certifications Data
  certifications: [
    {
      id: 1,
      title: "Ethical Hacking",
      issuer: "NPTEL",
      date: "Jul-Oct 2024",
      credentialId: "NPTEL-EH-2024",
      verificationUrl: "https://nptel.ac.in/courses/106/105/106105190/",
      icon: "fas fa-shield-alt",
      skills: ["Cybersecurity", "Ethical Hacking", "Network Security"],
      certificateLink: "Ethical_Hacking_NPTEL.jpg"
    },
    {
      id: 2,
      title: "Programming in Java",
      issuer: "NPTEL",
      date: "Jan-Apr 2025",
      credentialId: "NPTEL-JAVA-2025",
      verificationUrl: "https://nptel.ac.in/courses/106/105/106105151/",
      icon: "fab fa-java",
      skills: ["Java", "Object-Oriented Programming", "Data Structures"],
      certificateLink: "Programming_in_Java_NPTEL.jpg"
    },
    {
      id: 3,
      title: "Java Course",
      issuer: "Udemy",
      date: "Oct-Dec 2024",
      credentialId: "UDEMY-JAVA-2024",
      verificationUrl: "https://www.udemy.com/course/java-the-complete-java-developer-course/",
      icon: "fab fa-java",
      skills: ["Java Programming", "Spring Framework", "Web Development"],
      certificateLink: "Java_Udemy.jpg"
    },
    {
      id: 4,
      title: "Java Programming",
      issuer: "Great Learning Academy",
      date: "2024",
      credentialId: "GLA-JAVA-2024",
      verificationUrl: "https://www.mygreatlearning.com/academy/learn-for-free/courses/java-programming",
      icon: "fab fa-java",
      skills: ["Java Fundamentals", "Programming Concepts"],
      certificateLink: "Java_Great_Learning.jpg"
    },
    {
      id: 5,
      title: "Generative AI",
      issuer: "LinkedIn Learning",
      date: "2024",
      credentialId: "LI-AI-2024",
      verificationUrl: "https://www.linkedin.com/learning/generative-ai-foundations",
      icon: "fas fa-robot",
      skills: ["Artificial Intelligence", "Machine Learning", "Generative AI"],
      certificateLink: "Gen_AI_Linkden.jpg"
    },
    {
      id: 6,
      title: "AI for Students: Build Your Own Generative AI Model",
      issuer: "Workshop by AI expert Mr. Abhinav Devaguptapu",
      date: "2024",
      credentialId: "NXT-WAVE-AI-2024",
      verificationUrl: "https://www.nxtwave.tech/",
      icon: "fas fa-brain",
      skills: ["Generative AI", "AI Model Building", "Machine Learning"],
      certificateLink: "Nxt_Wave.jpg"
    }
  ],

  // Testimonials Data (optional)
  testimonials: [
    {
      id: 1,
      name: "Sarah Johnson",
      position: "Senior Developer",
      company: "TechCorp Solutions",
      content: "Disha is an exceptional developer with a keen eye for detail. Her ability to learn quickly and adapt to new technologies is impressive.",
      avatar: "https://via.placeholder.com/80x80/64FFDA/0A192F?text=SJ",
      rating: 5
    },
    {
      id: 2,
      name: "Mike Chen",
      position: "Project Manager",
      company: "StartupXYZ",
      content: "Working with Disha was a pleasure. She consistently delivered high-quality code and was always willing to go the extra mile.",
      avatar: "https://via.placeholder.com/80x80/64FFDA/0A192F?text=MC",
      rating: 5
    }
  ]
};

// ===== DATA RENDERING FUNCTIONS =====
class DataRenderer {
  static renderProjects() {
    const projectsGrid = document.querySelector('.projects-grid');
    if (!projectsGrid) return;

    const featuredProjects = portfolioData.projects.filter(project => project.featured);
    
    projectsGrid.innerHTML = featuredProjects.map(project => `
      <div class="project-card hover-lift" data-category="${project.category}">
        <div class="project-image">
          <img src="${project.image}" alt="${project.title}" loading="lazy">
          <div class="project-overlay">
            <div class="project-links">
              ${project.liveDemo ? `<a href="${project.liveDemo}" target="_blank" class="project-link" aria-label="View live demo">
                <i class="fas fa-external-link-alt"></i>
              </a>` : ''}
              <a href="${project.sourceCode}" target="_blank" class="project-link" aria-label="View source code">
                <i class="fab fa-github"></i>
              </a>
            </div>
          </div>
        </div>
        <div class="project-content">
          <h3 class="project-title">${project.title}</h3>
          <p class="project-description">${project.description}</p>
          <div class="project-technologies">
            ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
          </div>
        </div>
      </div>
    `).join('');
  }

  static renderExperience() {
    const timeline = document.querySelector('.timeline');
    if (!timeline) return;

    timeline.innerHTML = portfolioData.experience.map((exp, index) => `
      <div class="timeline-item ${index % 2 === 0 ? 'left' : 'right'}">
        <div class="timeline-content">
          <div class="timeline-header">
            <h3 class="timeline-title">${exp.position}</h3>
            <div class="timeline-company">${exp.company} • ${exp.location}</div>
            <div class="timeline-duration">${exp.duration}</div>
          </div>
          <p class="timeline-description">${exp.description}</p>
          <ul class="timeline-responsibilities">
            ${exp.responsibilities.map(resp => `<li>${resp}</li>`).join('')}
          </ul>
          <div class="timeline-technologies">
            ${exp.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
          </div>
          ${exp.certificateLink ? `<div class="timeline-certificate">
            <a href="${exp.certificateLink}" target="_blank" class="achievement-link certificate-link">
              <i class="fas fa-certificate"></i> View Certificate
            </a>
          </div>` : ''}
        </div>
      </div>
    `).join('');
  }

  static renderEducation() {
    const educationTimeline = document.querySelector('.education-timeline');
    if (!educationTimeline) return;

    educationTimeline.innerHTML = portfolioData.education.map((edu, index) => `
      <div class="education-item ${index % 2 === 0 ? 'left' : 'right'}">
        <div class="education-content">
          <div class="education-header">
            <div class="education-icon">
              <i class="${edu.icon}"></i>
            </div>
            <div class="education-info">
              <h3>${edu.degree}</h3>
              <div class="education-institution">${edu.institution}</div>
              <div class="education-duration">${edu.duration}</div>
            </div>
          </div>
          <div class="education-details">
            <div class="education-field">${edu.field}</div>
            <div class="education-location">${edu.location}</div>
            <div class="education-grade">${edu.grade}</div>
          </div>
        </div>
      </div>
    `).join('');
  }

  static renderAchievements() {
    const achievementsGrid = document.querySelector('.achievements-grid');
    if (!achievementsGrid) return;

    const allItems = [
      ...portfolioData.achievements.map(item => ({...item, type: 'achievement'})),
      ...portfolioData.certifications.map(item => ({...item, type: 'certification'}))
    ];

    achievementsGrid.innerHTML = allItems.map(item => `
      <div class="achievement-item hover-lift">
        <div class="achievement-icon">
          ${item.iconImage ? `<img src="${item.iconImage}" alt="${item.title}" class="achievement-icon-img">` : `<i class="${item.icon}"></i>`}
        </div>
        <div class="achievement-content">
          <h3 class="achievement-title">${item.title}</h3>
          <div class="achievement-organization">${item.organization || item.issuer}</div>
          <div class="achievement-date">${item.date}</div>
          <p class="achievement-description">${item.description || 'Professional certification'}</p>
          ${item.certificateLink ? `<a href="${item.certificateLink}" target="_blank" class="achievement-link certificate-link">
            <i class="fas fa-certificate"></i> View Certificate
          </a>` : ''}
          ${item.verificationUrl ? `<a href="${item.verificationUrl}" target="_blank" class="achievement-link">
            <i class="fas fa-external-link-alt"></i> Verify
          </a>` : ''}
        </div>
      </div>
    `).join('');
  }
}

// ===== INITIALIZE DATA RENDERING =====
document.addEventListener('DOMContentLoaded', () => {
  DataRenderer.renderProjects();
  DataRenderer.renderEducation();
  DataRenderer.renderExperience();
  DataRenderer.renderAchievements();

  console.log('Portfolio data rendered successfully!');
});

// ===== EXPORT DATA =====
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { portfolioData, DataRenderer };
}
