// ===== DATA JAVASCRIPT FILE =====

// ===== PORTFOLIO DATA =====
const portfolioData = {
  // Personal Information
  personal: {
    name: "<PERSON>sha T V",
    title: "Creative Technologist & Aspiring Developer",
    email: "<EMAIL>",
    location: "India",
    bio: [
      "Hello! I'm <PERSON><PERSON>, a passionate technologist with a love for creating digital experiences that make a difference. My journey in technology began with curiosity and has evolved into a deep commitment to continuous learning and innovation.",
      "I enjoy working on projects that challenge me to think creatively and solve complex problems. Whether it's developing web applications, exploring new frameworks, or collaborating on team projects, I'm always eager to expand my skill set and contribute meaningfully.",
      "When I'm not coding, you can find me exploring the latest tech trends, participating in hackathons, or working on personal projects that push the boundaries of what I know."
    ]
  },

  // Social Links
  social: {
    linkedin: "https://linkedin.com/in/dishatv",
    github: "https://github.com/dishatv",
    twitter: "https://twitter.com/dishatv",
    email: "mailto:<EMAIL>"
  },

  // Skills Data
  skills: {
    languages: [
      { name: "Python", icon: "fab fa-python", level: 90 },
      { name: "JavaScript", icon: "fab fa-js-square", level: 85 },
      { name: "Java", icon: "fab fa-java", level: 80 },
      { name: "HTML5", icon: "fab fa-html5", level: 95 },
      { name: "CSS3", icon: "fab fa-css3-alt", level: 90 },
      { name: "TypeScript", icon: "fas fa-code", level: 75 }
    ],
    frameworks: [
      { name: "React", icon: "fab fa-react", level: 85 },
      { name: "Node.js", icon: "fab fa-node-js", level: 80 },
      { name: "Django", icon: "fas fa-code", level: 75 },
      { name: "Bootstrap", icon: "fab fa-bootstrap", level: 90 },
      { name: "Express.js", icon: "fas fa-server", level: 70 },
      { name: "Vue.js", icon: "fab fa-vuejs", level: 65 }
    ],
    tools: [
      { name: "Git", icon: "fab fa-git-alt", level: 90 },
      { name: "Docker", icon: "fab fa-docker", level: 70 },
      { name: "Figma", icon: "fab fa-figma", level: 85 },
      { name: "VS Code", icon: "fas fa-code", level: 95 },
      { name: "Terminal", icon: "fas fa-terminal", level: 85 },
      { name: "Postman", icon: "fas fa-paper-plane", level: 80 }
    ],
    databases: [
      { name: "MySQL", icon: "fas fa-database", level: 85 },
      { name: "MongoDB", icon: "fas fa-leaf", level: 80 },
      { name: "PostgreSQL", icon: "fas fa-server", level: 75 },
      { name: "Redis", icon: "fas fa-memory", level: 65 }
    ]
  },

  // Projects Data
  projects: [
    {
      id: 1,
      title: "E-Commerce Platform",
      description: "A full-stack e-commerce solution with React frontend and Node.js backend, featuring user authentication, payment integration, and admin dashboard.",
      image: "https://via.placeholder.com/400x250/64FFDA/0A192F?text=E-Commerce+Platform",
      technologies: ["React", "Node.js", "MongoDB", "Stripe", "JWT"],
      liveDemo: "https://demo-ecommerce.dishatv.com",
      sourceCode: "https://github.com/dishatv/ecommerce-platform",
      featured: true,
      category: "Full Stack"
    },
    {
      id: 2,
      title: "Task Management App",
      description: "A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
      image: "https://via.placeholder.com/400x250/64FFDA/0A192F?text=Task+Manager",
      technologies: ["Vue.js", "Express.js", "Socket.io", "PostgreSQL"],
      liveDemo: "https://taskmanager.dishatv.com",
      sourceCode: "https://github.com/dishatv/task-manager",
      featured: true,
      category: "Web App"
    },
    {
      id: 3,
      title: "Weather Dashboard",
      description: "A responsive weather dashboard that provides current weather conditions, forecasts, and interactive maps using multiple weather APIs.",
      image: "https://via.placeholder.com/400x250/64FFDA/0A192F?text=Weather+Dashboard",
      technologies: ["JavaScript", "Chart.js", "OpenWeather API", "CSS3"],
      liveDemo: "https://weather.dishatv.com",
      sourceCode: "https://github.com/dishatv/weather-dashboard",
      featured: false,
      category: "Frontend"
    },
    {
      id: 4,
      title: "Blog CMS",
      description: "A content management system for bloggers with markdown support, SEO optimization, and social media integration.",
      image: "https://via.placeholder.com/400x250/64FFDA/0A192F?text=Blog+CMS",
      technologies: ["Django", "Python", "SQLite", "Bootstrap"],
      liveDemo: "https://blog-cms.dishatv.com",
      sourceCode: "https://github.com/dishatv/blog-cms",
      featured: false,
      category: "Backend"
    },
    {
      id: 5,
      title: "Portfolio Website",
      description: "A responsive portfolio website showcasing projects and skills with modern design, dark mode, and smooth animations.",
      image: "https://via.placeholder.com/400x250/64FFDA/0A192F?text=Portfolio+Website",
      technologies: ["HTML5", "CSS3", "JavaScript", "GSAP"],
      liveDemo: "https://dishatv.com",
      sourceCode: "https://github.com/dishatv/portfolio",
      featured: true,
      category: "Frontend"
    },
    {
      id: 6,
      title: "API Gateway",
      description: "A microservices API gateway with rate limiting, authentication, and request routing capabilities.",
      image: "https://via.placeholder.com/400x250/64FFDA/0A192F?text=API+Gateway",
      technologies: ["Node.js", "Express.js", "Redis", "JWT", "Docker"],
      liveDemo: null,
      sourceCode: "https://github.com/dishatv/api-gateway",
      featured: false,
      category: "Backend"
    }
  ],

  // Experience/Internships Data
  experience: [
    {
      id: 1,
      company: "TechCorp Solutions",
      position: "Frontend Developer Intern",
      location: "Bangalore, India",
      duration: "June 2024 - August 2024",
      type: "Internship",
      description: "Worked on developing responsive web applications using React and modern JavaScript frameworks.",
      responsibilities: [
        "Developed and maintained React components for the company's main product",
        "Collaborated with the design team to implement pixel-perfect UI designs",
        "Optimized application performance resulting in 30% faster load times",
        "Participated in code reviews and followed agile development practices"
      ],
      technologies: ["React", "JavaScript", "CSS3", "Git", "Figma"],
      achievements: [
        "Successfully delivered 5 major features ahead of schedule",
        "Received positive feedback from senior developers for code quality"
      ]
    },
    {
      id: 2,
      company: "StartupXYZ",
      position: "Full Stack Developer Intern",
      location: "Remote",
      duration: "January 2024 - May 2024",
      type: "Internship",
      description: "Contributed to both frontend and backend development of a SaaS platform for small businesses.",
      responsibilities: [
        "Built RESTful APIs using Node.js and Express.js",
        "Implemented user authentication and authorization systems",
        "Created responsive frontend interfaces using Vue.js",
        "Worked with MongoDB for data storage and retrieval"
      ],
      technologies: ["Vue.js", "Node.js", "MongoDB", "Express.js", "JWT"],
      achievements: [
        "Reduced API response time by 40% through optimization",
        "Implemented security best practices preventing potential vulnerabilities"
      ]
    },
    {
      id: 3,
      company: "Digital Agency Pro",
      position: "Web Development Intern",
      location: "Mumbai, India",
      duration: "September 2023 - December 2023",
      type: "Internship",
      description: "Assisted in creating websites for various clients ranging from small businesses to large enterprises.",
      responsibilities: [
        "Developed custom WordPress themes and plugins",
        "Implemented SEO best practices for client websites",
        "Collaborated with designers to create responsive layouts",
        "Provided technical support and maintenance for existing websites"
      ],
      technologies: ["WordPress", "PHP", "MySQL", "HTML5", "CSS3", "jQuery"],
      achievements: [
        "Successfully launched 8 client websites with 100% client satisfaction",
        "Improved website loading speeds by an average of 50%"
      ]
    }
  ],

  // Achievements Data
  achievements: [
    {
      id: 1,
      title: "Best Innovation Award",
      organization: "TechFest 2024",
      date: "March 2024",
      description: "Won first place in the innovation category for developing an AI-powered task management solution.",
      icon: "fas fa-trophy",
      category: "Competition"
    },
    {
      id: 2,
      title: "Hackathon Winner",
      organization: "CodeSprint Hackathon",
      date: "January 2024",
      description: "Led a team of 4 developers to create a sustainable transportation app in 48 hours.",
      icon: "fas fa-medal",
      category: "Hackathon"
    },
    {
      id: 3,
      title: "Open Source Contributor",
      organization: "GitHub",
      date: "Ongoing",
      description: "Active contributor to various open source projects with 50+ merged pull requests.",
      icon: "fab fa-github",
      category: "Open Source"
    },
    {
      id: 4,
      title: "Dean's List",
      organization: "University",
      date: "2023-2024",
      description: "Achieved Dean's List recognition for academic excellence with GPA above 3.8.",
      icon: "fas fa-graduation-cap",
      category: "Academic"
    }
  ],

  // Certifications Data
  certifications: [
    {
      id: 1,
      title: "AWS Certified Cloud Practitioner",
      issuer: "Amazon Web Services",
      date: "February 2024",
      credentialId: "AWS-CCP-2024-001",
      verificationUrl: "https://aws.amazon.com/verification",
      icon: "fab fa-aws",
      skills: ["Cloud Computing", "AWS Services", "Security"]
    },
    {
      id: 2,
      title: "React Developer Certification",
      issuer: "Meta",
      date: "December 2023",
      credentialId: "META-REACT-2023-456",
      verificationUrl: "https://coursera.org/verify/meta-react",
      icon: "fab fa-react",
      skills: ["React", "JavaScript", "Frontend Development"]
    },
    {
      id: 3,
      title: "Google Analytics Certified",
      issuer: "Google",
      date: "November 2023",
      credentialId: "GA-CERT-2023-789",
      verificationUrl: "https://skillshop.exceedlms.com/student/path/508845",
      icon: "fab fa-google",
      skills: ["Analytics", "Data Analysis", "Digital Marketing"]
    },
    {
      id: 4,
      title: "MongoDB Developer Certification",
      issuer: "MongoDB University",
      date: "October 2023",
      credentialId: "MONGO-DEV-2023-123",
      verificationUrl: "https://university.mongodb.com/certification",
      icon: "fas fa-leaf",
      skills: ["MongoDB", "Database Design", "NoSQL"]
    }
  ],

  // Testimonials Data (optional)
  testimonials: [
    {
      id: 1,
      name: "Sarah Johnson",
      position: "Senior Developer",
      company: "TechCorp Solutions",
      content: "Disha is an exceptional developer with a keen eye for detail. Her ability to learn quickly and adapt to new technologies is impressive.",
      avatar: "https://via.placeholder.com/80x80/64FFDA/0A192F?text=SJ",
      rating: 5
    },
    {
      id: 2,
      name: "Mike Chen",
      position: "Project Manager",
      company: "StartupXYZ",
      content: "Working with Disha was a pleasure. She consistently delivered high-quality code and was always willing to go the extra mile.",
      avatar: "https://via.placeholder.com/80x80/64FFDA/0A192F?text=MC",
      rating: 5
    }
  ]
};

// ===== DATA RENDERING FUNCTIONS =====
class DataRenderer {
  static renderProjects() {
    const projectsGrid = document.querySelector('.projects-grid');
    if (!projectsGrid) return;

    const featuredProjects = portfolioData.projects.filter(project => project.featured);
    
    projectsGrid.innerHTML = featuredProjects.map(project => `
      <div class="project-card hover-lift" data-category="${project.category}">
        <div class="project-image">
          <img src="${project.image}" alt="${project.title}" loading="lazy">
          <div class="project-overlay">
            <div class="project-links">
              ${project.liveDemo ? `<a href="${project.liveDemo}" target="_blank" class="project-link" aria-label="View live demo">
                <i class="fas fa-external-link-alt"></i>
              </a>` : ''}
              <a href="${project.sourceCode}" target="_blank" class="project-link" aria-label="View source code">
                <i class="fab fa-github"></i>
              </a>
            </div>
          </div>
        </div>
        <div class="project-content">
          <h3 class="project-title">${project.title}</h3>
          <p class="project-description">${project.description}</p>
          <div class="project-technologies">
            ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
          </div>
        </div>
      </div>
    `).join('');
  }

  static renderExperience() {
    const timeline = document.querySelector('.timeline');
    if (!timeline) return;

    timeline.innerHTML = portfolioData.experience.map((exp, index) => `
      <div class="timeline-item ${index % 2 === 0 ? 'left' : 'right'}">
        <div class="timeline-content">
          <div class="timeline-header">
            <h3 class="timeline-title">${exp.position}</h3>
            <div class="timeline-company">${exp.company} • ${exp.location}</div>
            <div class="timeline-duration">${exp.duration}</div>
          </div>
          <p class="timeline-description">${exp.description}</p>
          <ul class="timeline-responsibilities">
            ${exp.responsibilities.map(resp => `<li>${resp}</li>`).join('')}
          </ul>
          <div class="timeline-technologies">
            ${exp.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
          </div>
        </div>
      </div>
    `).join('');
  }

  static renderAchievements() {
    const achievementsGrid = document.querySelector('.achievements-grid');
    if (!achievementsGrid) return;

    const allItems = [
      ...portfolioData.achievements.map(item => ({...item, type: 'achievement'})),
      ...portfolioData.certifications.map(item => ({...item, type: 'certification'}))
    ];

    achievementsGrid.innerHTML = allItems.map(item => `
      <div class="achievement-item hover-lift">
        <div class="achievement-icon">
          <i class="${item.icon}"></i>
        </div>
        <div class="achievement-content">
          <h3 class="achievement-title">${item.title}</h3>
          <div class="achievement-organization">${item.organization || item.issuer}</div>
          <div class="achievement-date">${item.date}</div>
          <p class="achievement-description">${item.description || 'Professional certification'}</p>
          ${item.verificationUrl ? `<a href="${item.verificationUrl}" target="_blank" class="achievement-link">Verify</a>` : ''}
        </div>
      </div>
    `).join('');
  }
}

// ===== INITIALIZE DATA RENDERING =====
document.addEventListener('DOMContentLoaded', () => {
  DataRenderer.renderProjects();
  DataRenderer.renderExperience();
  DataRenderer.renderAchievements();
  
  console.log('Portfolio data rendered successfully!');
});

// ===== EXPORT DATA =====
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { portfolioData, DataRenderer };
}
