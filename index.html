<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disha T V - Portfolio</title>
    <meta name="description" content="Professional portfolio of Disha T V - Creative Technologist & Aspiring Developer">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- EmailJS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="#home">Disha TV</a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">Skills</a>
                </li>
                <li class="nav-item">
                    <a href="#education" class="nav-link">Education</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">Projects</a>
                </li>
                <li class="nav-item">
                    <a href="#internships" class="nav-link">Internships</a>
                </li>
                <li class="nav-item">
                    <a href="#achievements" class="nav-link">Achievements</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="nav-toggle">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="hamburger" id="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content fade-in">
                <p class="hero-greeting">Hi, my name is</p>
                <h1 class="hero-name">Disha T V.</h1>
                <h2 class="hero-title">A Creative Technologist & Aspiring Developer.</h2>
                <p class="hero-description">
                    I'm passionate about creating innovative digital solutions and bringing ideas to life through code.
                    Currently focused on building exceptional user experiences and learning cutting-edge technologies.
                </p>
                <div class="hero-buttons">
                    <a href="#projects" class="cta-button primary">View My Work</a>
                    <a href="Disha_Resume.pdf" target="_blank" class="cta-button secondary">
                        <i class="fas fa-download"></i> Download Resume
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">About Me</h2>
            </div>
            <div class="about-content">
                <div class="about-text">
                    <div class="about-description">
                        <p>
                            Hello! I am Disha, a passionate technologist with a love for creating digital experiences that make a difference. My journey in technology began with curiosity and has evolved into a deep commitment to continuous learning and innovation.
                        </p>
                        <p>
                            I am a driven and detail-oriented individual with a strong foundation in programming, particularly in Java and data structures. My academic journey has been complemented by hands-on experience in various projects, allowing me to explore both front-end and back-end development.
                        </p>
                        <p>
                            I pride myself on being an adaptive learner and a collaborative team player, always striving to approach challenges with creativity and strategic thinking. Outside the academic realm, I am passionate about singing and dancing, which allow me to express myself artistically, and I find solace in reading novels, as it nurtures my imagination and widens my intellectual horizon
                        </p>
                        <p>
                            I am eager to apply my skills in a dynamic environment where I can contribute meaningfully while continuing to grow both personally and professionally.
                        </p>
                    </div>
                </div>
                <div class="about-image">
                    <div class="image-wrapper">
                        <img src="Disha_Image.jpeg" alt="Disha T V" class="profile-image">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Skills & Technologies</h2>
            </div>
            <div class="skills-grid">
                <div class="skill-category">
                    <h3 class="category-title">Languages</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <i class="fas fa-code"></i>
                            <span>C</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-java"></i>
                            <span>Java</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-js-square"></i>
                            <span>JavaScript</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-php"></i>
                            <span>PHP</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-html5"></i>
                            <span>HTML5</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-css3-alt"></i>
                            <span>CSS3</span>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3 class="category-title">Frameworks & Libraries</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <i class="fab fa-react"></i>
                            <span>React</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-node-js"></i>
                            <span>Node.js</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-bootstrap"></i>
                            <span>Bootstrap</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-wind"></i>
                            <span>Tailwind CSS</span>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3 class="category-title">Tools & Technologies</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <i class="fab fa-git-alt"></i>
                            <span>Git</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-linux"></i>
                            <span>Linux</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-file-excel"></i>
                            <span>Excel</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-file-code"></i>
                            <span>LaTeX</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-envelope"></i>
                            <span>Nodemailer</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-database"></i>
                            <span>Supabase</span>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3 class="category-title">Databases</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <i class="fas fa-database"></i>
                            <span>MySQL</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-leaf"></i>
                            <span>MongoDB</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-hdd"></i>
                            <span>LocalBase</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-save"></i>
                            <span>Local Storage</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Education Section -->
    <section id="education" class="education">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Education</h2>
            </div>
            <div class="education-timeline">
                <!-- Education items will be dynamically generated -->
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Featured Projects</h2>
                <p class="section-subtitle">A collection of projects that showcase my skills and passion for development</p>
            </div>
            <div class="projects-grid">
                <!-- Project cards will be dynamically generated -->
            </div>
        </div>
    </section>

    <!-- Internships Section -->
    <section id="internships" class="internships">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Internships</h2>
            </div>
            <div class="timeline">
                <!-- Timeline items will be dynamically generated -->
            </div>
        </div>
    </section>

    <!-- Achievements Section -->
    <section id="achievements" class="achievements">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Achievements & Certifications</h2>
            </div>
            <div class="achievements-content">
                <div class="achievements-grid">
                    <!-- Achievement items will be dynamically generated -->
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Get In Touch</h2>
                <p class="section-subtitle">
                    I'm always open to discussing new opportunities, interesting projects, or just having a chat about technology.
                </p>
            </div>
            <div class="contact-content">
                <div class="contact-grid">
                    <div class="contact-info">
                        <h3 class="contact-info-title">Get In Touch</h3>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-file-pdf"></i>
                            <a href="Disha_Resume.pdf" target="_blank">Download Resume</a>
                        </div>
                        <div class="social-links">
                            <a href="https://www.linkedin.com/in/disha-t-v-033ab8264" target="_blank" class="social-link" aria-label="LinkedIn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="https://github.com/disha9353" target="_blank" class="social-link" aria-label="GitHub">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="https://instagram.com/disha.gowda_" target="_blank" class="social-link" aria-label="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="https://www.salesforce.com/trailblazer/itbg4hujrhthu29hdf" target="_blank" class="social-link" aria-label="Salesforce Trailblazer">
                                <i class="fab fa-salesforce"></i>
                            </a>
                        </div>
                    </div>

                    <div class="contact-form-container">
                        <h3 class="contact-form-title">Send Me a Message</h3>
                        <form class="contact-form" id="contact-form" action="https://formspree.io/f/mgvejqko" method="POST">
                            <input type="hidden" name="_to" value="<EMAIL>">
                            <input type="hidden" name="_subject" value="New Contact Form Message from Portfolio">
                            <input type="hidden" name="_next" value="thank-you.html">
                            <div class="form-group">
                                <label for="name">Name</label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="subject">Subject</label>
                                <input type="text" id="subject" name="subject" required>
                            </div>
                            <div class="form-group">
                                <label for="message">Message</label>
                                <textarea id="message" name="message" rows="5" required></textarea>
                            </div>
                            <button type="submit" class="form-submit-btn">
                                <i class="fas fa-paper-plane"></i>
                                Send Message
                            </button>
                            <div class="form-alternative">
                                <p>Or send me an email directly:</p>
                                <a href="mailto:<EMAIL>?subject=Portfolio Contact&body=Hi Disha,%0D%0A%0D%0AI would like to get in touch with you.%0D%0A%0D%0ABest regards," class="email-direct-btn">
                                    <i class="fas fa-envelope"></i>
                                    Email Directly
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 Disha T V. All Rights Reserved.</p>
                <div class="footer-social">
                    <a href="https://www.linkedin.com/in/disha-t-v-033ab8264" target="_blank" class="social-link" aria-label="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://github.com/disha9353" target="_blank" class="social-link" aria-label="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://instagram.com/disha.gowda_" target="_blank" class="social-link" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="https://www.salesforce.com/trailblazer/itbg4hujrhthu29hdf" target="_blank" class="social-link" aria-label="Salesforce Trailblazer">
                        <i class="fab fa-salesforce"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/data.js"></script>
</body>
</html>
