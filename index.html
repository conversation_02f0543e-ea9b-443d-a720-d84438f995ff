<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disha T V - Portfolio</title>
    <meta name="description" content="Professional portfolio of Disha T V - Creative Technologist & Aspiring Developer">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="#home">Disha TV</a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">Skills</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">Projects</a>
                </li>
                <li class="nav-item">
                    <a href="#internships" class="nav-link">Internships</a>
                </li>
                <li class="nav-item">
                    <a href="#achievements" class="nav-link">Achievements</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="nav-toggle">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="hamburger" id="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content fade-in">
                <p class="hero-greeting">Hi, my name is</p>
                <h1 class="hero-name">Disha T V.</h1>
                <h2 class="hero-title">A Creative Technologist & Aspiring Developer.</h2>
                <p class="hero-description">
                    I'm passionate about creating innovative digital solutions and bringing ideas to life through code. 
                    Currently focused on building exceptional user experiences and learning cutting-edge technologies.
                </p>
                <a href="#projects" class="cta-button">View My Work</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">About Me</h2>
            </div>
            <div class="about-content">
                <div class="about-text">
                    <div class="about-description">
                        <p>
                            Hello! I'm Disha, a passionate technologist with a love for creating digital experiences 
                            that make a difference. My journey in technology began with curiosity and has evolved 
                            into a deep commitment to continuous learning and innovation.
                        </p>
                        <p>
                            I enjoy working on projects that challenge me to think creatively and solve complex problems. 
                            Whether it's developing web applications, exploring new frameworks, or collaborating on 
                            team projects, I'm always eager to expand my skill set and contribute meaningfully.
                        </p>
                        <p>
                            When I'm not coding, you can find me exploring the latest tech trends, participating in 
                            hackathons, or working on personal projects that push the boundaries of what I know.
                        </p>
                    </div>
                </div>
                <div class="about-image">
                    <div class="image-wrapper">
                        <img src="Disha_Image.jpeg" alt="Disha T V" class="profile-image">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Skills & Technologies</h2>
            </div>
            <div class="skills-grid">
                <div class="skill-category">
                    <h3 class="category-title">Languages</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <i class="fab fa-python"></i>
                            <span>Python</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-js-square"></i>
                            <span>JavaScript</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-java"></i>
                            <span>Java</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-html5"></i>
                            <span>HTML5</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-css3-alt"></i>
                            <span>CSS3</span>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3 class="category-title">Frameworks & Libraries</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <i class="fab fa-react"></i>
                            <span>React</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-node-js"></i>
                            <span>Node.js</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-code"></i>
                            <span>Django</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-bootstrap"></i>
                            <span>Bootstrap</span>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3 class="category-title">Tools & Technologies</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <i class="fab fa-git-alt"></i>
                            <span>Git</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-docker"></i>
                            <span>Docker</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-figma"></i>
                            <span>Figma</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-terminal"></i>
                            <span>Terminal</span>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h3 class="category-title">Databases</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <i class="fas fa-database"></i>
                            <span>MySQL</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-leaf"></i>
                            <span>MongoDB</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-server"></i>
                            <span>PostgreSQL</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Featured Projects</h2>
                <p class="section-subtitle">A collection of projects that showcase my skills and passion for development</p>
            </div>
            <div class="projects-grid">
                <!-- Project cards will be dynamically generated -->
            </div>
        </div>
    </section>

    <!-- Internships Section -->
    <section id="internships" class="internships">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Experience</h2>
            </div>
            <div class="timeline">
                <!-- Timeline items will be dynamically generated -->
            </div>
        </div>
    </section>

    <!-- Achievements Section -->
    <section id="achievements" class="achievements">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Achievements & Certifications</h2>
            </div>
            <div class="achievements-content">
                <div class="achievements-grid">
                    <!-- Achievement items will be dynamically generated -->
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Get In Touch</h2>
                <p class="section-subtitle">
                    I'm always open to discussing new opportunities, interesting projects, or just having a chat about technology.
                </p>
            </div>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div class="social-links">
                        <a href="#" class="social-link" aria-label="LinkedIn">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="social-link" aria-label="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="social-link" aria-label="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 Disha T V. All Rights Reserved.</p>
                <div class="footer-social">
                    <a href="#" class="social-link" aria-label="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/data.js"></script>
</body>
</html>
