// ===== ANIMATIONS JAVASCRIPT FILE =====

// ===== SCROLL ANIMATION MANAGER =====
class ScrollAnimationManager {
  constructor() {
    this.animatedElements = [];
    this.observer = null;
    this.init();
  }

  init() {
    this.setupIntersectionObserver();
    this.addAnimationClasses();
    this.bindEvents();
  }

  setupIntersectionObserver() {
    const options = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animateElement(entry.target);
        }
      });
    }, options);
  }

  addAnimationClasses() {
    // Add scroll animation classes to elements
    const elementsToAnimate = [
      { selector: '.hero-content', animation: 'fade-in-up' },
      { selector: '.about-text', animation: 'scroll-animate-left' },
      { selector: '.about-image', animation: 'scroll-animate-right' },
      { selector: '.skill-category', animation: 'scroll-animate-scale' },
      { selector: '.project-card', animation: 'scroll-animate' },
      { selector: '.timeline-item', animation: 'scroll-animate-left' },
      { selector: '.achievement-item', animation: 'scroll-animate' },
      { selector: '.contact-content', animation: 'fade-in-up' }
    ];

    elementsToAnimate.forEach(({ selector, animation }) => {
      const elements = document.querySelectorAll(selector);
      elements.forEach((element, index) => {
        element.classList.add(animation);
        
        // Add stagger delay for multiple elements
        if (elements.length > 1) {
          element.style.animationDelay = `${index * 0.1}s`;
        }
        
        this.observer.observe(element);
      });
    });
  }

  animateElement(element) {
    element.classList.add('animate');
    this.observer.unobserve(element);
  }

  bindEvents() {
    // Re-observe elements when they go out of view (optional)
    window.addEventListener('scroll', this.throttle(() => {
      // Additional scroll-based animations can be added here
    }, 16));
  }

  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}

// ===== TYPING ANIMATION =====
class TypingAnimation {
  constructor(element, texts, options = {}) {
    this.element = element;
    this.texts = Array.isArray(texts) ? texts : [texts];
    this.options = {
      typeSpeed: 100,
      backSpeed: 50,
      backDelay: 2000,
      startDelay: 500,
      loop: true,
      showCursor: true,
      cursorChar: '|',
      ...options
    };
    
    this.currentTextIndex = 0;
    this.currentCharIndex = 0;
    this.isDeleting = false;
    this.isWaiting = false;
    
    this.init();
  }

  init() {
    if (this.options.showCursor) {
      this.element.style.borderRight = `2px solid var(--accent-color)`;
    }
    
    setTimeout(() => this.type(), this.options.startDelay);
  }

  type() {
    const currentText = this.texts[this.currentTextIndex];
    
    if (this.isDeleting) {
      this.currentCharIndex--;
    } else {
      this.currentCharIndex++;
    }
    
    this.element.textContent = currentText.substring(0, this.currentCharIndex);
    
    let typeSpeed = this.isDeleting ? this.options.backSpeed : this.options.typeSpeed;
    
    if (!this.isDeleting && this.currentCharIndex === currentText.length) {
      // Finished typing current text
      typeSpeed = this.options.backDelay;
      this.isDeleting = true;
    } else if (this.isDeleting && this.currentCharIndex === 0) {
      // Finished deleting current text
      this.isDeleting = false;
      this.currentTextIndex = (this.currentTextIndex + 1) % this.texts.length;
      typeSpeed = this.options.typeSpeed;
    }
    
    if (this.options.loop || this.currentTextIndex < this.texts.length) {
      setTimeout(() => this.type(), typeSpeed);
    }
  }
}

// ===== PARTICLE ANIMATION =====
class ParticleAnimation {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      particleCount: 50,
      particleSize: 2,
      particleSpeed: 1,
      particleColor: 'rgba(100, 255, 218, 0.3)',
      connectionDistance: 100,
      ...options
    };
    
    this.particles = [];
    this.canvas = null;
    this.ctx = null;
    this.animationId = null;
    
    this.init();
  }

  init() {
    this.createCanvas();
    this.createParticles();
    this.animate();
    this.bindEvents();
  }

  createCanvas() {
    this.canvas = document.createElement('canvas');
    this.canvas.style.position = 'absolute';
    this.canvas.style.top = '0';
    this.canvas.style.left = '0';
    this.canvas.style.pointerEvents = 'none';
    this.canvas.style.zIndex = '-1';
    
    this.container.style.position = 'relative';
    this.container.appendChild(this.canvas);
    
    this.ctx = this.canvas.getContext('2d');
    this.resizeCanvas();
  }

  resizeCanvas() {
    const rect = this.container.getBoundingClientRect();
    this.canvas.width = rect.width;
    this.canvas.height = rect.height;
  }

  createParticles() {
    for (let i = 0; i < this.options.particleCount; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        vx: (Math.random() - 0.5) * this.options.particleSpeed,
        vy: (Math.random() - 0.5) * this.options.particleSpeed,
        size: Math.random() * this.options.particleSize + 1
      });
    }
  }

  animate() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Update and draw particles
    this.particles.forEach(particle => {
      // Update position
      particle.x += particle.vx;
      particle.y += particle.vy;
      
      // Bounce off edges
      if (particle.x < 0 || particle.x > this.canvas.width) particle.vx *= -1;
      if (particle.y < 0 || particle.y > this.canvas.height) particle.vy *= -1;
      
      // Draw particle
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      this.ctx.fillStyle = this.options.particleColor;
      this.ctx.fill();
    });
    
    // Draw connections
    this.drawConnections();
    
    this.animationId = requestAnimationFrame(() => this.animate());
  }

  drawConnections() {
    for (let i = 0; i < this.particles.length; i++) {
      for (let j = i + 1; j < this.particles.length; j++) {
        const dx = this.particles[i].x - this.particles[j].x;
        const dy = this.particles[i].y - this.particles[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < this.options.connectionDistance) {
          const opacity = 1 - (distance / this.options.connectionDistance);
          this.ctx.beginPath();
          this.ctx.moveTo(this.particles[i].x, this.particles[i].y);
          this.ctx.lineTo(this.particles[j].x, this.particles[j].y);
          this.ctx.strokeStyle = `rgba(100, 255, 218, ${opacity * 0.2})`;
          this.ctx.lineWidth = 1;
          this.ctx.stroke();
        }
      }
    }
  }

  bindEvents() {
    window.addEventListener('resize', () => this.resizeCanvas());
  }

  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    if (this.canvas) {
      this.canvas.remove();
    }
  }
}

// ===== HOVER EFFECTS MANAGER =====
class HoverEffectsManager {
  constructor() {
    this.init();
  }

  init() {
    this.addHoverEffects();
    this.addMouseFollower();
  }

  addHoverEffects() {
    // Add hover effects to interactive elements
    const hoverElements = [
      { selector: '.skill-item', effect: 'lift' },
      { selector: '.project-card', effect: 'lift-glow' },
      { selector: '.social-link', effect: 'scale' },
      { selector: '.cta-button', effect: 'glow' }
    ];

    hoverElements.forEach(({ selector, effect }) => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        element.classList.add(`hover-${effect}`);
      });
    });
  }

  addMouseFollower() {
    const follower = document.createElement('div');
    follower.className = 'mouse-follower';
    follower.style.cssText = `
      position: fixed;
      width: 20px;
      height: 20px;
      background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      opacity: 0;
      transition: opacity 0.3s ease;
      mix-blend-mode: difference;
    `;
    
    document.body.appendChild(follower);
    
    let mouseX = 0;
    let mouseY = 0;
    let followerX = 0;
    let followerY = 0;
    
    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
      follower.style.opacity = '0.5';
    });
    
    document.addEventListener('mouseleave', () => {
      follower.style.opacity = '0';
    });
    
    const animateFollower = () => {
      followerX += (mouseX - followerX) * 0.1;
      followerY += (mouseY - followerY) * 0.1;
      
      follower.style.left = followerX - 10 + 'px';
      follower.style.top = followerY - 10 + 'px';
      
      requestAnimationFrame(animateFollower);
    };
    
    animateFollower();
  }
}

// ===== LOADING ANIMATION =====
class LoadingAnimation {
  constructor() {
    this.init();
  }

  init() {
    this.createLoader();
    this.showLoader();
  }

  createLoader() {
    const loader = document.createElement('div');
    loader.id = 'page-loader';
    loader.innerHTML = `
      <div class="loader-content">
        <div class="loader-logo">DTV</div>
        <div class="loader-spinner"></div>
        <div class="loader-text">Loading...</div>
      </div>
    `;
    
    const styles = `
      #page-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--bg-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        transition: opacity 0.5s ease;
      }
      
      .loader-content {
        text-align: center;
      }
      
      .loader-logo {
        font-size: 3rem;
        font-weight: 700;
        color: var(--accent-color);
        font-family: var(--font-mono);
        margin-bottom: 2rem;
        animation: pulse 2s infinite;
      }
      
      .loader-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid var(--border-color);
        border-top: 3px solid var(--accent-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
      }
      
      .loader-text {
        color: var(--text-secondary);
        font-size: 1rem;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = styles;
    document.head.appendChild(styleSheet);
    
    document.body.appendChild(loader);
  }

  showLoader() {
    const loader = document.getElementById('page-loader');
    
    window.addEventListener('load', () => {
      setTimeout(() => {
        loader.style.opacity = '0';
        setTimeout(() => {
          loader.remove();
          document.body.classList.add('loaded');
        }, 500);
      }, 1000);
    });
  }
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
  // Initialize animation managers
  const scrollAnimationManager = new ScrollAnimationManager();
  const hoverEffectsManager = new HoverEffectsManager();
  const loadingAnimation = new LoadingAnimation();
  
  // Initialize typing animation for hero title (if needed)
  const heroTitle = document.querySelector('.hero-title');
  if (heroTitle && heroTitle.dataset.typing) {
    new TypingAnimation(heroTitle, [
      'A Creative Technologist & Aspiring Developer.',
      'Building Digital Experiences.',
      'Passionate About Innovation.'
    ]);
  }
  
  // Initialize particle animation for hero section (optional)
  const heroSection = document.querySelector('.hero');
  if (heroSection && heroSection.dataset.particles) {
    new ParticleAnimation(heroSection);
  }
  
  console.log('Animations initialized successfully!');
});

// ===== EXPORT FOR TESTING =====
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    ScrollAnimationManager,
    TypingAnimation,
    ParticleAnimation,
    HoverEffectsManager,
    LoadingAnimation
  };
}
